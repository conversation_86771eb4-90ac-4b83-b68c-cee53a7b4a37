# Mysql数据库 - 开发环境也应使用环境变量避免敏感信息泄露
spring:
  datasource:
    dynamic:
      primary: master #设置默认的数据源或者数据源组,默认值即为master
      strict: false #设置严格模式,默认false不启动. 启动后在未匹配到指定数据源时候回抛出异常,不启动会使用默认数据源.
      datasource:
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ${DB_MASTER_URL:**********************************************************************************************************************************************************************************************************}
          username: ${DB_MASTER_USERNAME:root}
          password: ${DB_MASTER_PASSWORD:root}
        dataCenter:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ${DB_DATACENTER_URL:*****************************************************************************************************************************************************************************************************}
          username: ${DB_DATACENTER_USERNAME:root}
          password: ${DB_DATACENTER_PASSWORD:root}
        jyfx:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ${DB_JYFX_URL:**************************************************************************************************************************************************************************************************}
          username: ${DB_JYFX_USERNAME:root}
          password: ${DB_JYFX_PASSWORD:root}
        huiliu:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ${DB_HUILIU_URL:*****************************************************************************************************************************************************************************************************}
          username: ${DB_HUILIU_USERNAME:root}
          password: ${DB_HUILIU_PASSWORD:root}
  redis:
    host: localhost
    port: 6379
    password:
#验证码相关配置 去除日志打印
logging:
  level:
    com.anji: off
    com.concise: debug
zzd:
  qrcode:
    appkey: xinyoulinxi_dingoa-HdI4jzQba63
    appsecret: T8CGkeESb610Y0cb9Rijqh6O6RGBut8Cv2d7gcSS
    remark: xinyoulinxi_dingoa
    domain: openplatform-pro.ding.zj.gov.cn
    tenantid: 196729
# 认证中心配置
auth:
  center:
    # 客户端ID（必填）- 从认证中心获取
    client-id: "risen_IXVyVScZYjmTBq4x3kalog"
    # 客户端密钥（必填）- 从认证中心获取
    client-secret: "jc9soUvlh2e8rNHPoa1xNClunJyjeMjTo1UgsdknGRPJ8mqeSBKCGNadzNXDuHAi"
    # 认证中心授权地址（必填）
    authorize-url: "https://one.lpxxfw.cn:7200/auth"
    # 获取token的地址（必填）
    token-url: "https://************:7100/typt/public/user/getUserInfoByToken"
    # 撤销token的地址（必填）
    revoke-url: "https://one.lpxxfw.cn:7200/auth/oauth2/revoke"
    # 登出地址（必填）
    logout-url: "https://one.lpxxfw.cn:7200/auth/logout"
    # 回调地址（必填）- 必须与注册时的地址一致，使用实际域名
    redirect-uri: "http://10.32.153.42:8050/#/transfer"
    # 授权范围（可选）- 默认为AUTH
    scope: "AUTH"
