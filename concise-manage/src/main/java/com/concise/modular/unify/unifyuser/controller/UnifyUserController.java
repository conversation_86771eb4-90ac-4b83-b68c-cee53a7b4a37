
package com.concise.modular.unify.unifyuser.controller;


import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.consts.CommonConstant;
import com.concise.common.enums.CommonStatusEnum;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.core.context.login.LoginContextHolder;
import com.concise.core.login.SysLoginUser;
import com.concise.modular.unify.lxuserinfo.service.LxUserInfoService;
import com.concise.modular.unify.pushrecord.service.PushManagerService;
import com.concise.modular.unify.unifyuser.entity.LxExtendInfosVo;
import com.concise.modular.unify.unifyuser.entity.LxUserVo;
import com.concise.modular.unify.unifyuser.entity.ReceiveBody;
import com.concise.modular.unify.unifyuser.entity.UnifyUser;
import com.concise.modular.unify.unifyuser.param.UnifyUserParam;
import com.concise.modular.unify.unifyuser.service.UnifyUserService;
import com.concise.modular.unify.unifyuser.user.SignVo;
import com.concise.modular.unify.unifyuser.user.UnifyVo;
import com.concise.modular.unify.utils.SM4Util;
import com.concise.sys.core.enums.AdminTypeEnum;
import com.concise.sys.core.enums.SexEnum;
import com.concise.sys.modular.auth.service.AuthService;
import com.concise.sys.modular.emp.entity.SysEmp;
import com.concise.sys.modular.emp.service.SysEmpService;
import com.concise.sys.modular.org.entity.SysOrg;
import com.concise.sys.modular.org.service.SysOrgService;
import com.concise.sys.modular.user.entity.SysUser;
import com.concise.sys.modular.user.entity.SysUserRole;
import com.concise.sys.modular.user.service.SysUserRoleService;
import com.concise.sys.modular.user.service.SysUserService;
import com.qcloud.cos.utils.Md5Utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.Method;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 浙政钉用户表控制器
 *
 * <AUTHOR>
 * @date 2022-04-14 14:13:15
 */
@Slf4j
@Api(tags = "浙政钉用户表")
@RestController
public class UnifyUserController {

    @Resource
    private UnifyUserService unifyUserService;

    @Resource
    private SysUserService sysUserService;
    @Resource
    private AuthService authService;
    @Resource
    private SysEmpService sysEmpService;
    @Resource
    private SysUserRoleService sysUserRoleService;

    @Resource
    private SysOrgService sysOrgService;

    @Resource
    private LxUserInfoService lxUserInfoService;

    @Resource
    private PushManagerService pushManagerService;

    public static final String PUSH_KEY = "ZSUlE1hP0jSK8eh8";


    /**
     * 查询浙政钉用户表
     *
     * <AUTHOR>
     * @date 2022-04-14 14:13:15
     */
    @Permission
    @GetMapping("/unifyUser/page")
    @ApiOperation(value = "浙政钉用户表_分页查询", notes = "浙政钉用户表_分页查询")
    @BusinessLog(title = "浙政钉用户表_分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(UnifyUserParam unifyUserParam) {
        return new SuccessResponseData(unifyUserService.page(unifyUserParam));
    }

    @GetMapping("/unifyUser/getSign")
    @ApiOperation(value = "登录用户获取签名", notes = "登录用户获取签名")
    @BusinessLog(title = "登录用户获取签名", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getSign() {
        SysLoginUser sysLoginUser = LoginContextHolder.me().getSysLoginUser();
        SignVo signVo = new SignVo();
        signVo.setId(sysLoginUser.getId());
        signVo.setDate(new Date());
        String jsonString = JSONObject.toJSONString(signVo);
        String encryptDatas = SM4Util.encryptDatas(jsonString, SM4Util.SECRET_KEY);
        return new SuccessResponseData(encryptDatas);
    }


    @PostMapping("/unifyUser/unifyUser")
    @ApiOperation(value = "新增浙政钉用户")
    public ResponseData testPostUserData(@RequestParam String unifyUser) {
        String decryptDatas = SM4Util.decryptDatas(unifyUser, SM4Util.SECRET_KEY);
        UnifyVo unifyVo = JSON.parseObject(decryptDatas, UnifyVo.class);
        asyncSaveUnifyUser(unifyVo);
        log.info(decryptDatas);
        return ResponseData.success("新增成功");
    }

    @PutMapping("/unifyUser/unifyUser")
    @ApiOperation(value = "更新浙政钉用户")
    public ResponseData testPutUserData(@RequestParam String unifyUser) {
        String decryptDatas = SM4Util.decryptDatas(unifyUser, SM4Util.SECRET_KEY);
        UnifyVo unifyVo = JSON.parseObject(decryptDatas, UnifyVo.class);
        asyncUpdateUser(unifyVo);
        log.info(decryptDatas);
        return ResponseData.success("更新成功");
    }

    @DeleteMapping("/unifyUser/unifyUser")
    @ApiOperation(value = "删除浙政钉用户")
    public ResponseData testDeleteUserData(@RequestParam String id) {
        String decryptDatas = SM4Util.decryptDatas(id, SM4Util.SECRET_KEY);
        asyncDeleteUser(decryptDatas);
        log.info(decryptDatas);
        return ResponseData.success("删除成功");
    }

    @GetMapping("/unifyUser/updateUserToSystemUser")
    @ApiOperation(value = "浙政钉用户表_同步到系统用户", notes = "浙政钉用户表_同步到系统用户")
    @BusinessLog(title = "浙政钉用户表_同步到系统用户", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData updateUserToSystemUser() {
        unifyUserService.updateUserToSystemUser();
        return new SuccessResponseData();
    }

    /**
     * 添加浙政钉用户表
     *
     * <AUTHOR>
     * @date 2022-04-14 14:13:15
     */
    @Permission
    @PostMapping("/unifyUser/add")
    @ApiOperation(value = "浙政钉用户表_增加", notes = "浙政钉用户表_增加")
    @BusinessLog(title = "浙政钉用户表_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(UnifyUserParam.add.class) UnifyUserParam unifyUserParam) {
        unifyUserService.add(unifyUserParam);
        return new SuccessResponseData();
    }

    /**
     * 删除浙政钉用户表，可批量删除
     *
     * <AUTHOR>
     * @date 2022-04-14 14:13:15
     */
    @Permission
    @PostMapping("/unifyUser/delete")
    @ApiOperation(value = "浙政钉用户表_删除", notes = "浙政钉用户表_删除")
    @BusinessLog(title = "浙政钉用户表_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(UnifyUserParam.delete.class) List<UnifyUserParam> unifyUserParamList) {
        unifyUserService.delete(unifyUserParamList);
        return new SuccessResponseData();
    }

    /**
     * 编辑浙政钉用户表
     *
     * <AUTHOR>
     * @date 2022-04-14 14:13:15
     */
    @Permission
    @PostMapping("/unifyUser/edit")
    @ApiOperation(value = "浙政钉用户表_编辑", notes = "浙政钉用户表_编辑")
    @BusinessLog(title = "浙政钉用户表_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(UnifyUserParam.edit.class) UnifyUserParam unifyUserParam) {
        unifyUserService.edit(unifyUserParam);
        return new SuccessResponseData();
    }

    /**
     * 查看浙政钉用户表
     *
     * <AUTHOR>
     * @date 2022-04-14 14:13:15
     */
    @Permission
    @GetMapping("/unifyUser/detail")
    @ApiOperation(value = "浙政钉用户表_详情", notes = "浙政钉用户表_详情")
    @BusinessLog(title = "浙政钉用户表_详情", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(UnifyUserParam.detail.class) UnifyUserParam unifyUserParam) {
        return new SuccessResponseData(unifyUserService.detail(unifyUserParam));
    }

    @GetMapping("/unifyUser/signIn")
    @ApiOperation(value = "免密登录", notes = "免密登录")
    @BusinessLog(title = "免密登录", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData signIn(@RequestParam String sign) {
        String url = "http://10.68.179.210:8080/api/unifyUser/verify";
//        String url = "http://192.168.2.40:6070/api/unifyUser/verify";
        Map<String, Object> map = new HashMap<>();
        map.put("sign", sign);
        HttpRequest request = new HttpRequest(url);
        String jsonString = JSONObject.toJSONString(map);
        request.body(jsonString);
        request.contentType("application/json");
        request.method(Method.POST);
        String body = request.execute().body();
        JSONObject jsonObject = JSONObject.parseObject(body);
        boolean status = (boolean) jsonObject.get("success");
        if (status) {
            Object name = jsonObject.get("data");
            JSONObject object = JSONObject.parseObject(name.toString());
            String id = (String) object.get("id");
            if (StringUtils.isNotBlank(id)) {
                SysUser userById = sysUserService.getUserById(id);
                String token = authService.doLogin(userById);
                return ResponseData.success(token);
            }
        }
        return ResponseData.error("登录失败！");
    }

    /**
     * 浙政钉用户表列表
     *
     * <AUTHOR>
     * @date 2022-04-14 14:13:15
     */
    @Permission
    @GetMapping("/unifyUser/list")
    @ApiOperation(value = "浙政钉用户表_列表", notes = "浙政钉用户表_列表")
    @BusinessLog(title = "浙政钉用户表_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(UnifyUserParam unifyUserParam) {
        return new SuccessResponseData(unifyUserService.list(unifyUserParam));
    }

    @PostMapping("/unifyUser/userReceive")
    @ApiOperation(value = "浙政钉用户表_接收用户推送", notes = "浙政钉用户表_接收用户推送")
    @BusinessLog(title = "浙政钉用户表_接收用户推送", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData userReceive(@RequestBody ReceiveBody receiveBody) {
        String decryptedDatas = SM4Util.decryptDatas(receiveBody.getCode(), PUSH_KEY);
        LxUserVo lxUserVo = JSONObject.parseObject(decryptedDatas, LxUserVo.class);
        if (ObjectUtil.isNotNull(lxUserVo)) {
            // 保存到新的浙政钉用户信息表
            try {
                lxUserInfoService.saveLxUserInfo(lxUserVo);

                // 异步推送原始参数到所有启用的目标系统
                pushManagerService.pushToAllTargets(receiveBody);
            } catch (Exception e) {
            }

            if (lxUserVo.getOperatorType() == 2) {
                sysUserService.removeById(lxUserVo.getId());
                sysEmpService.remove(new QueryWrapper<SysEmp>().lambda().eq(SysEmp::getJobNum, lxUserVo.getId()));
                sysUserRoleService.remove(new QueryWrapper<SysUserRole>().lambda().eq(SysUserRole::getUserId, lxUserVo.getId()));
            } else {
                SysUser sysUser = sysUserService.getById(lxUserVo.getId());
                if (ObjectUtil.isNotEmpty(sysUser)) {
                    sysUser.setAccount(lxUserVo.getUserName());
                    sysUser.setName(lxUserVo.getName());
                    sysUser.setNickName(lxUserVo.getName());
                    sysUserService.updateById(sysUser);
                } else {
                    SysUser user = new SysUser();
                    user.setId(lxUserVo.getId());
                    user.setAccount(lxUserVo.getUserName());
                    user.setPassword(DigestUtil.md5Hex(CommonConstant.DEFAULT_PASSWORD));
                    user.setNickName(lxUserVo.getName());
                    user.setName(lxUserVo.getName());
                    user.setSex(lxUserVo.getSex());
                    user.setStatus(CommonStatusEnum.ENABLE.getCode());
                    user.setAdminType(AdminTypeEnum.NONE.getCode());
                    sysUserService.save(user);
                }
                //处理部门
                List<LxExtendInfosVo> extendInfos = lxUserVo.getExtendInfos();
                if (CollectionUtil.isNotEmpty(extendInfos)) {
                    for (LxExtendInfosVo extendInfo : extendInfos) {
                        if (ObjectUtil.isNotNull(extendInfo.getJzjg())) {
                            int count = sysEmpService.count(new QueryWrapper<SysEmp>().lambda().eq(SysEmp::getJobNum, lxUserVo.getId()).eq(SysEmp::getOrgId, extendInfo.getJzjg()));
                            SysOrg sysOrg = sysOrgService.getById(extendInfo.getJzjg());
                            if (count == 0 && ObjectUtil.isNotNull(sysOrg)) {
                                SysEmp sysEmp = new SysEmp();
                                sysEmp.setId(lxUserVo.getId());
                                sysEmp.setJobNum(lxUserVo.getId());
                                sysEmp.setOrgId(extendInfo.getJzjg());
                                sysEmp.setOrgName(extendInfo.getJzjgName());
                                sysEmpService.save(sysEmp);
                            }
                        }
                    }
                }


            }


            return ResponseData.success();
        }

        return ResponseData.error("解析错误！");
    }

    /**
     * 导出浙政钉用户表
     *
     * <AUTHOR>
     * @date 2022-04-14 14:13:15
     */
    @Permission
    @GetMapping("/unifyUser/export")
    @ApiOperation(value = "浙政钉用户表_导出", notes = "浙政钉用户表_导出")
    @BusinessLog(title = "浙政钉用户表_导出", opType = LogAnnotionOpTypeEnum.EXPORT)
    public void export(UnifyUserParam unifyUserParam) {
        unifyUserService.export(unifyUserParam);
    }

    /**
     * 异步保存统一用户
     *
     * @param unifyVo
     */
    @Async
    public void asyncSaveUnifyUser(UnifyVo unifyVo) {
        UnifyUser unifyUser = new UnifyUser();
        BeanUtils.copyProperties(unifyVo, unifyUser);
        //保存所有的信息，备用
        String unifyString = JSON.toJSONString(unifyVo);
        //取用其中一个
        String belongs = unifyVo.getBelongs().get(0).getBelongOuUuid();
        String emails = unifyVo.getEmails().get(0).getValue();
        String phoneNumbers = unifyVo.getPhoneNumbers().get(0).getValue();
        String extendField = unifyVo.getExtendField().toString();
        String moziDeptCode = unifyVo.getMoziDeptCode();
        unifyUser.setJsonString(unifyString);
        unifyUser.setBelongs(belongs);
        unifyUser.setEmail(emails);
        unifyUser.setPhoneNumbers(phoneNumbers);
        unifyUser.setExtendfield(extendField);
        unifyUser.setMoziDeptCode(moziDeptCode);
        unifyUser.setCreateTime(DateUtil.date());

        unifyUserService.saveOrUpdate(unifyUser);
        SysUser user = sysUserService.getById(unifyUser.getId());
        if (ObjectUtil.isNull(user)) {
            SysUser sysUser = new SysUser();
            sysUser.setId(unifyUser.getId());
            sysUser.setAccount(unifyUser.getUserName());
            sysUser.setPassword(Md5Utils.md5Hex("Tyyh@2021"));
            sysUser.setNickName(unifyUser.getDisplayName());
            sysUser.setName(unifyUser.getDisplayName());
            sysUser.setAdminType(AdminTypeEnum.NONE.getCode());
            sysUser.setSex(SexEnum.NONE.getCode());
            sysUser.setPhone(unifyUser.getUserName());
            sysUser.setStatus(CommonStatusEnum.ENABLE.getCode());
            sysUserService.save(sysUser);
        } else {
            user.setId(unifyUser.getId());
            user.setAccount(unifyUser.getUserName());
            user.setNickName(unifyUser.getDisplayName());
            user.setName(unifyUser.getDisplayName());
            user.setAdminType(AdminTypeEnum.NONE.getCode());
            user.setSex(SexEnum.NONE.getCode());
            user.setPhone(unifyUser.getUserName());
            user.setStatus(CommonStatusEnum.ENABLE.getCode());
            sysUserService.updateById(user);
        }
        SysEmp sysEmp = new SysEmp();
        sysEmp.setId(unifyUser.getId());
        sysEmp.setJobNum(unifyUser.getId());
        if (StringUtils.isNotBlank(unifyUser.getCorrectOrgId())) {
            sysEmp.setOrgId(unifyUser.getCorrectOrgId());
            sysEmp.setOrgName(unifyUser.getCorrectOrgName());
            sysEmpService.saveOrUpdate(sysEmp);
        }


    }

    @Async
    public void asyncUpdateUser(UnifyVo unifyVo) {
        UnifyUser unifyUser = new UnifyUser();
        BeanUtils.copyProperties(unifyVo, unifyUser);
        //保存所有的信息，备用
        String unifyString = JSON.toJSONString(unifyVo);
        //取用其中一个
        String belongs = unifyVo.getBelongs().get(0).getBelongOuUuid();
        String emails = unifyVo.getEmails().get(0).getValue();
        String phoneNumbers = unifyVo.getPhoneNumbers().get(0).getValue();
        String extendField = unifyVo.getExtendField().toString();
        String moziDeptCode = unifyVo.getMoziDeptCode();
        unifyUser.setJsonString(unifyString);
        unifyUser.setBelongs(belongs);
        unifyUser.setEmail(emails);
        unifyUser.setMoziDeptCode(moziDeptCode);
        unifyUser.setPhoneNumbers(phoneNumbers);
        unifyUser.setExtendfield(extendField);
        unifyUser.setUpdateTime(DateUtil.date());
        unifyUserService.saveOrUpdate(unifyUser);
        SysUser user = sysUserService.getById(unifyUser.getId());
        if (ObjectUtil.isNull(user)) {
            SysUser sysUser = new SysUser();
            sysUser.setId(unifyUser.getId());
            sysUser.setAccount(unifyUser.getUserName());
            sysUser.setPassword(Md5Utils.md5Hex("Tyyh@2021"));
            sysUser.setNickName(unifyUser.getDisplayName());
            sysUser.setName(unifyUser.getDisplayName());
            sysUser.setAdminType(AdminTypeEnum.NONE.getCode());
            sysUser.setSex(SexEnum.NONE.getCode());
            sysUser.setPhone(unifyUser.getUserName());
            sysUser.setStatus(CommonStatusEnum.ENABLE.getCode());
            sysUserService.save(sysUser);
        } else {
            user.setId(unifyUser.getId());
            user.setAccount(unifyUser.getUserName());
            user.setNickName(unifyUser.getDisplayName());
            user.setName(unifyUser.getDisplayName());
            user.setAdminType(AdminTypeEnum.NONE.getCode());
            user.setSex(SexEnum.NONE.getCode());
            user.setPhone(unifyUser.getUserName());
            user.setStatus(CommonStatusEnum.ENABLE.getCode());
            sysUserService.updateById(user);
        }
        SysEmp sysEmp = new SysEmp();
        sysEmp.setId(unifyUser.getId());
        sysEmp.setJobNum(unifyUser.getId());
        if (StringUtils.isNotBlank(unifyUser.getCorrectOrgId())) {
            sysEmp.setOrgId(unifyUser.getCorrectOrgId());
            sysEmp.setOrgName(unifyUser.getCorrectOrgName());
            sysEmpService.saveOrUpdate(sysEmp);
        }


    }

    /**
     * 异步删除用户
     *
     * @param id
     */
    @Async
    public void asyncDeleteUser(String id) {
        UnifyUser unifyUser = unifyUserService.getById(id);
        unifyUser.setDeleteTime(new Date());
        unifyUser.setDeleteFlag("1");
        unifyUserService.updateById(unifyUser);
    }

    /**
     * 测试接口 - 接收推送数据
     *
     * @param requestBody 推送的数据
     * @return 响应结果
     */
    @PostMapping("/unifyUser/testPushReceive")
    @ApiOperation(value = "测试接口_接收推送数据", notes = "用于测试接收各种推送数据的接口")
    @BusinessLog(title = "测试接口_接收推送数据", opType = LogAnnotionOpTypeEnum.OTHER)
    public ResponseData testPushReceive(@RequestBody String requestBody) {
        try {
            log.info("=== 测试推送接收接口 ===");
            log.info("接收到的原始数据: {}", requestBody);

            // 尝试解析为JSON对象
            try {
                JSONObject jsonObject = JSONObject.parseObject(requestBody);
                log.info("解析后的JSON数据: {}", jsonObject.toJSONString());

                // 如果包含加密字段，尝试解密
                if (jsonObject.containsKey("code")) {
                    String encryptedCode = jsonObject.getString("code");
                    try {
                        String decryptedData = SM4Util.decryptDatas(encryptedCode, PUSH_KEY);
                        log.info("解密后的数据: {}", decryptedData);
                    } catch (Exception e) {
                        log.warn("使用默认密钥解密失败: {}", e.getMessage());
                    }
                }

            } catch (Exception e) {
                log.info("数据不是标准JSON格式，作为普通字符串处理");
            }

            // 记录接收时间
            log.info("数据接收时间: {}", DateUtil.now());

            return ResponseData.success("测试推送接收成功，数据已记录到日志中");

        } catch (Exception e) {
            log.error("测试推送接收失败", e);
            return ResponseData.error("测试推送接收失败: " + e.getMessage());
        }
    }


}
